import { useState, useCallback } from 'react';
import { useApi, errorApiRef } from '@backstage/core-plugin-api';
import { ApiRequest, ApiResponse, ApiEnvironment, TestResult } from '../../../types';
import { createNewRequest, sendRequest } from '../utils/requestUtils';
import { executeTests } from '../../../utils/testExecutor';
import { generateTestScript } from '../../../utils/testGenerator';
import { useTypedTaskManager } from './useTaskManager';

export const useRequest = (collections: any[], setCollections: (collections: any[]) => void) => {
  const errorApi = useApi(errorApiRef);
  const taskManager = useTypedTaskManager();

  // State for the current request
  const [currentRequest, setCurrentRequest] = useState<ApiRequest>(createNewRequest());

  // State for the current response
  const [currentResponse, setCurrentResponse] = useState<ApiResponse | null>(null);

  // State for loading indicator
  const [isLoading, setIsLoading] = useState<boolean>(false);

  // State for request form tabs
  const [tabValue, setTabValue] = useState(0);

  // State for response tabs
  const [responseTabValue, setResponseTabValue] = useState(0);

  // State for test-related functionality
  const [isGeneratingTests, setIsGeneratingTests] = useState<boolean>(false);
  const [isRunningTests, setIsRunningTests] = useState<boolean>(false);
  const [testResults, setTestResults] = useState<TestResult[]>([]);
  const [testError, setTestError] = useState<string | null>(null);

  // State for pre-request script functionality
  const [isSavingPreRequestScript, setIsSavingPreRequestScript] = useState<boolean>(false);
  const [preRequestScriptError, setPreRequestScriptError] = useState<string | null>(null);

  // Handle tab change
  const handleTabChange = useCallback((event: React.ChangeEvent<{}>, newValue: number) => {
    setTabValue(newValue);
  }, []);

  // Handle response tab change
  const handleResponseTabChange = useCallback((event: React.ChangeEvent<{}>, newValue: number) => {
    setResponseTabValue(newValue);
  }, []);

  // Handle method change
  const handleMethodChange = useCallback((event: React.ChangeEvent<{ value: unknown }>) => {
    setCurrentRequest(prev => ({
      ...prev,
      method: event.target.value as any,
    }));
  }, []);

  // Handle URL change
  const handleUrlChange = useCallback((event: React.ChangeEvent<HTMLInputElement>) => {
    setCurrentRequest(prev => ({
      ...prev,
      url: event.target.value,
    }));
  }, []);

  // Handle send request with TaskMaster
  const handleSendRequest = useCallback(async (environment?: ApiEnvironment) => {
    setIsLoading(true);
    setCurrentResponse(null);
    setResponseTabValue(0); // Reset to body tab
    setTestResults([]); // Clear test results

    try {
      return await taskManager.executeRequestTask('send', async () => {
        // Use the sendRequest function to send the request directly
        const apiResponse = await sendRequest(currentRequest, environment);

        // Update response with size if not already present
        if (apiResponse.body && typeof apiResponse.size === 'undefined') {
          apiResponse.size = new Blob([apiResponse.body]).size;
        }

        setCurrentResponse(apiResponse);
        return { success: true, response: apiResponse };
      });
    } catch (error) {
      errorApi.post(error instanceof Error ? error : new Error(String(error)));
      return { success: false, error };
    } finally {
      setIsLoading(false);
    }
  }, [currentRequest, errorApi, taskManager]);

  // Handle generate tests with TaskMaster
  const handleGenerateTests = useCallback(async () => {
    if (!currentResponse) {
      return { success: false, error: 'No response available' };
    }

    setIsGeneratingTests(true);
    setTestError(null);

    try {
      return await taskManager.executeTestTask('generate', async () => {
        // Parse the response body as JSON if possible
        let responseData = null;
        try {
          if (currentResponse.body &&
              currentResponse.headers &&
              currentResponse.headers['content-type'] &&
              currentResponse.headers['content-type'].includes('application/json')) {
            responseData = JSON.parse(currentResponse.body);
          }
        } catch (error) {
          // Silently handle JSON parsing errors
          responseData = null;
        }

        // Generate test script using the utility function
        const testScript = generateTestScript({
          status: currentResponse.status,
          headers: currentResponse.headers,
          data: responseData
        });

        // Update the current request with the test script
        setCurrentRequest(prev => ({
          ...prev,
          testScript: testScript
        }));

        return { success: true, testScript };
      });
    } catch (error) {
      setTestError(error instanceof Error ? error.message : String(error));
      return { success: false, error };
    } finally {
      setIsGeneratingTests(false);
    }
  }, [currentResponse, taskManager]);

  // Handle run tests with TaskMaster
  const handleRunTests = useCallback(async () => {
    if (!currentRequest.testScript || !currentResponse) {
      return { success: false, error: 'No test script or response available' };
    }

    setIsRunningTests(true);
    setTestError(null);

    try {
      return await taskManager.executeTestTask('run', async () => {
        // Execute tests using the utility function
        const results = await executeTests(currentRequest.testScript, currentResponse);
        setTestResults(results);

        // Update the current request with the test results
        setCurrentRequest(prev => ({
          ...prev,
          lastTestResults: results
        }));

        return { success: true, results };
      });
    } catch (error) {
      setTestError(error instanceof Error ? error.message : String(error));
      return { success: false, error };
    } finally {
      setIsRunningTests(false);
    }
  }, [currentRequest.testScript, currentResponse, taskManager]);

  // Handle save tests
  const handleSaveTests = useCallback((testScript: string) => {
    // Update the current request with the test script
    setCurrentRequest(prev => ({
      ...prev,
      testScript: testScript
    }));

    // If this request is part of a collection, update it there too
    const selectedItemId = currentRequest.id;
    if (selectedItemId) {
      const updatedCollections = collections.map(collection => {
        if (collection.requests[selectedItemId]) {
          const updatedRequests = {
            ...collection.requests,
            [selectedItemId]: {
              ...collection.requests[selectedItemId],
              testScript: testScript
            }
          };

          return {
            ...collection,
            requests: updatedRequests
          };
        }
        return collection;
      });

      setCollections(updatedCollections);
    }

    return { success: true };
  }, [currentRequest.id, collections, setCollections]);

  // Handle save pre-request script
  const handleSavePreRequestScript = useCallback((script: string) => {
    setIsSavingPreRequestScript(true);
    setPreRequestScriptError(null);

    try {
      // Update the current request with the pre-request script
      setCurrentRequest(prev => ({
        ...prev,
        preRequestScript: script
      }));

      // If this request is part of a collection, update it there too
      const selectedItemId = currentRequest.id;
      if (selectedItemId) {
        const updatedCollections = collections.map(collection => {
          if (collection.requests[selectedItemId]) {
            const updatedRequests = {
              ...collection.requests,
              [selectedItemId]: {
                ...collection.requests[selectedItemId],
                preRequestScript: script
              }
            };

            return {
              ...collection,
              requests: updatedRequests
            };
          }
          return collection;
        });

        setCollections(updatedCollections);
      }

      return { success: true };
    } catch (error) {
      setPreRequestScriptError(error instanceof Error ? error.message : String(error));
      return { success: false, error };
    } finally {
      setIsSavingPreRequestScript(false);
    }
  }, [currentRequest.id, collections, setCollections]);

  return {
    currentRequest,
    setCurrentRequest,
    currentResponse,
    setCurrentResponse,
    isLoading,
    tabValue,
    responseTabValue,
    isGeneratingTests,
    isRunningTests,
    testResults,
    testError,
    isSavingPreRequestScript,
    preRequestScriptError,
    taskManager,
    handleTabChange,
    handleResponseTabChange,
    handleMethodChange,
    handleUrlChange,
    handleSendRequest,
    handleGenerateTests,
    handleRunTests,
    handleSaveTests,
    handleSavePreRequestScript,
  };
};
