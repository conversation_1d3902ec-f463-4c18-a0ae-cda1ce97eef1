import React from 'react';
import {
  Paper,
  TextField,
  Button,
  FormControl,
  Select,
  MenuItem,
  CircularProgress,
  Tabs,
  Tab,
  Box,
} from '@material-ui/core';
import { makeStyles } from '@material-ui/core/styles';
import SendIcon from '@material-ui/icons/Send';

import { ApiRequest, ApiEnvironment, HttpMethod } from '../../../../types';
import { TabPanel } from '../TabPanel';
import { ParamsTab } from '../ParamsTab';
import { HeadersTab } from '../HeadersTab';
import { BodyTab } from '../BodyTab';
import { AuthTab } from '../AuthTab';
import { PreRequestScriptPanel } from '../../PreRequestScriptPanel';
import { TestGeneratorPanel } from '../../TestGeneratorPanel';
import { getUrlHelperText } from '../../utils/requestUtils';

const useStyles = makeStyles(theme => ({
  paper: {
    padding: theme.spacing(2),
    height: '100%',
  },
  urlBar: {
    display: 'flex',
    alignItems: 'center',
    marginBottom: theme.spacing(2),
  },
  methodSelect: {
    width: 120,
    marginRight: theme.spacing(2),
  },
  urlField: {
    flexGrow: 1,
    marginRight: theme.spacing(2),
  },
  sendButton: {
    marginLeft: theme.spacing(1),
  },
}));

interface RequestBuilderProps {
  currentRequest: ApiRequest;
  isLoading: boolean;
  tabValue: number;
  currentEnvironment?: ApiEnvironment;
  onRequestChange: (request: ApiRequest) => void;
  onMethodChange: (event: React.ChangeEvent<{ value: unknown }>) => void;
  onUrlChange: (event: React.ChangeEvent<HTMLInputElement>) => void;
  onTabChange: (event: React.ChangeEvent<{}>, newValue: number) => void;
  onSendRequest: () => void;
  onSavePreRequestScript: (script: string) => void;
  onSaveTests: (testScript: string) => void;
  onRunTests: (testScript: string) => void;
  isSavingPreRequestScript: boolean;
  preRequestScriptError: string | null;
  isGeneratingTests: boolean;
  isRunningTests: boolean;
  testError: string | null;
}

export const RequestBuilder: React.FC<RequestBuilderProps> = ({
  currentRequest,
  isLoading,
  tabValue,
  currentEnvironment,
  onRequestChange,
  onMethodChange,
  onUrlChange,
  onTabChange,
  onSendRequest,
  onSavePreRequestScript,
  onSaveTests,
  onRunTests,
  isSavingPreRequestScript,
  preRequestScriptError,
  isGeneratingTests,
  isRunningTests,
  testError,
}) => {
  const classes = useStyles();

  const renderUrlBar = () => (
    <div className={classes.urlBar}>
      <FormControl className={classes.methodSelect}>
        <Select
          value={currentRequest.method}
          onChange={onMethodChange}
          variant="outlined"
        >
          <MenuItem value="GET">GET</MenuItem>
          <MenuItem value="POST">POST</MenuItem>
          <MenuItem value="PUT">PUT</MenuItem>
          <MenuItem value="DELETE">DELETE</MenuItem>
          <MenuItem value="PATCH">PATCH</MenuItem>
          <MenuItem value="HEAD">HEAD</MenuItem>
          <MenuItem value="OPTIONS">OPTIONS</MenuItem>
        </Select>
      </FormControl>
      <TextField
        className={classes.urlField}
        variant="outlined"
        size="small"
        placeholder="Enter request URL"
        value={currentRequest.url}
        onChange={onUrlChange}
        helperText={getUrlHelperText(currentRequest.url)}
      />
      <Button
        variant="contained"
        color="primary"
        startIcon={isLoading ? <CircularProgress size={20} color="inherit" /> : <SendIcon />}
        onClick={onSendRequest}
        disabled={isLoading || !currentRequest.url}
      >
        Send
      </Button>
    </div>
  );

  const renderTabs = () => (
    <Tabs
      value={tabValue}
      onChange={onTabChange}
      indicatorColor="primary"
      textColor="primary"
    >
      <Tab label="Params" />
      <Tab label="Headers" />
      <Tab label="Body" />
      <Tab label="Auth" />
      <Tab label="Pre-request" />
      <Tab label="Tests" />
    </Tabs>
  );

  const renderTabPanels = () => (
    <>
      {/* Params tab */}
      <TabPanel value={tabValue} index={0}>
        <ParamsTab
          params={currentRequest.params}
          onParamsChange={(params) => onRequestChange({ ...currentRequest, params })}
        />
      </TabPanel>

      {/* Headers tab */}
      <TabPanel value={tabValue} index={1}>
        <HeadersTab
          headers={currentRequest.headers}
          onHeadersChange={(headers) => onRequestChange({ ...currentRequest, headers })}
        />
      </TabPanel>

      {/* Body tab */}
      <TabPanel value={tabValue} index={2}>
        <BodyTab
          body={currentRequest.body}
          onBodyChange={(body) => onRequestChange({ ...currentRequest, body })}
        />
      </TabPanel>

      {/* Auth tab */}
      <TabPanel value={tabValue} index={3}>
        <AuthTab
          auth={currentRequest.auth}
          onAuthChange={(auth) => onRequestChange({ ...currentRequest, auth })}
        />
      </TabPanel>

      {/* Pre-request script tab */}
      <TabPanel value={tabValue} index={4}>
        <PreRequestScriptPanel
          request={currentRequest}
          onSaveScript={onSavePreRequestScript}
          isSaving={isSavingPreRequestScript}
          error={preRequestScriptError}
        />
      </TabPanel>

      {/* Tests tab */}
      <TabPanel value={tabValue} index={5}>
        <TestGeneratorPanel
          request={currentRequest}
          response={null} // Will be passed from parent when available
          onRunTests={onRunTests}
          onSaveTests={onSaveTests}
          isGenerating={isGeneratingTests}
          isRunning={isRunningTests}
          error={testError}
        />
      </TabPanel>
    </>
  );

  return (
    <Paper className={classes.paper}>
      {renderUrlBar()}
      {renderTabs()}
      {renderTabPanels()}
    </Paper>
  );
};
